import { NextRequest } from 'next/server'
import { POST } from '../register/route'
import { prisma } from '@/lib/prisma'

// Mock bcryptjs
jest.mock('bcryptjs', () => ({
  hash: jest.fn().mockResolvedValue('hashed-password'),
}))

const mockPrisma = prisma as jest.Mocked<typeof prisma>

describe('/api/auth/register', () => {
  beforeEach(() => {
    jest.clearAllMocks()
  })

  it('creates a new user successfully', async () => {
    // Mock Prisma calls
    mockPrisma.user.findUnique.mockResolvedValue(null) // No existing user
    mockPrisma.user.create.mockResolvedValue({
      id: 'test-user-id',
      name: 'Test User',
      email: '<EMAIL>',
      hashedPassword: 'hashed-password',
      image: null,
      createdAt: new Date(),
      updatedAt: new Date(),
    })

    const requestBody = {
      name: 'Test User',
      email: '<EMAIL>',
      password: 'password123',
    }

    const request = new NextRequest('http://localhost:3000/api/auth/register', {
      method: 'POST',
      body: JSON.stringify(requestBody),
      headers: {
        'Content-Type': 'application/json',
      },
    })

    const response = await POST(request)
    const data = await response.json()

    expect(response.status).toBe(201)
    expect(data.message).toBe('User created successfully')
    expect(data.user).toMatchObject({
      name: 'Test User',
      email: '<EMAIL>',
    })
    expect(data.user.hashedPassword).toBeUndefined() // Should not return password
  })

  it('returns error for missing fields', async () => {
    const requestBody = {
      name: 'Test User',
      // Missing email and password
    }

    const request = new NextRequest('http://localhost:3000/api/auth/register', {
      method: 'POST',
      body: JSON.stringify(requestBody),
      headers: {
        'Content-Type': 'application/json',
      },
    })

    const response = await POST(request)
    const data = await response.json()

    expect(response.status).toBe(400)
    expect(data.error).toBe('Missing required fields')
  })

  it('returns error for existing user', async () => {
    // Mock existing user
    mockPrisma.user.findUnique.mockResolvedValue({
      id: 'existing-user-id',
      name: 'Existing User',
      email: '<EMAIL>',
      hashedPassword: 'hashed-password',
      image: null,
      createdAt: new Date(),
      updatedAt: new Date(),
    })

    const requestBody = {
      name: 'Test User',
      email: '<EMAIL>', // Same email
      password: 'password123',
    }

    const request = new NextRequest('http://localhost:3000/api/auth/register', {
      method: 'POST',
      body: JSON.stringify(requestBody),
      headers: {
        'Content-Type': 'application/json',
      },
    })

    const response = await POST(request)
    const data = await response.json()

    expect(response.status).toBe(400)
    expect(data.error).toBe('User already exists')
  })

  it('handles database errors gracefully', async () => {
    // Mock Prisma to throw an error
    mockPrisma.user.findUnique.mockResolvedValue(null) // No existing user
    mockPrisma.user.create.mockRejectedValue(new Error('Database error'))

    const requestBody = {
      name: 'Test User',
      email: '<EMAIL>',
      password: 'password123',
    }

    const request = new NextRequest('http://localhost:3000/api/auth/register', {
      method: 'POST',
      body: JSON.stringify(requestBody),
      headers: {
        'Content-Type': 'application/json',
      },
    })

    const response = await POST(request)
    const data = await response.json()

    expect(response.status).toBe(500)
    expect(data.error).toBe('Internal server error')
  })
})
