import { NextRequest } from 'next/server'
import { GET, POST } from '../route'
import { prisma } from '@/lib/prisma'

// Mock NextAuth
jest.mock('next-auth', () => ({
  getServerSession: jest.fn(),
}))

import { getServerSession } from 'next-auth'
const mockGetServerSession = getServerSession as jest.MockedFunction<typeof getServerSession>
const mockPrisma = prisma as jest.Mocked<typeof prisma>

describe('/api/conversations/[conversationId]/messages', () => {
  const testUser1 = {
    id: 'test-user-1',
    name: 'Test User 1',
    email: '<EMAIL>',
    hashedPassword: 'hashed-password',
    image: null,
    createdAt: new Date(),
    updatedAt: new Date(),
  }

  const testUser2 = {
    id: 'test-user-2',
    name: 'Test User 2',
    email: '<EMAIL>',
    hashedPassword: 'hashed-password',
    image: null,
    createdAt: new Date(),
    updatedAt: new Date(),
  }

  const testConversation = {
    id: 'test-conversation-id',
    isGroup: false,
    name: null,
    createdAt: new Date(),
    updatedAt: new Date(),
  }

  beforeEach(() => {
    jest.clearAllMocks()

    mockGetServerSession.mockResolvedValue({
      user: { id: testUser1.id },
    } as any)
  })

  describe('GET /api/conversations/[conversationId]/messages', () => {
    it('returns messages for a conversation', async () => {
      // Mock conversation exists
      mockPrisma.conversation.findFirst.mockResolvedValue(testConversation)

      // Mock messages
      const mockMessages = [
        {
          id: 'msg-1',
          body: 'Hello!',
          image: null,
          senderId: testUser1.id,
          conversationId: testConversation.id,
          createdAt: new Date('2024-01-01T10:00:00Z'),
          updatedAt: new Date('2024-01-01T10:00:00Z'),
          sender: {
            id: testUser1.id,
            name: testUser1.name,
            image: testUser1.image,
          },
          seenBy: [],
        },
        {
          id: 'msg-2',
          body: 'Hi there!',
          image: null,
          senderId: testUser2.id,
          conversationId: testConversation.id,
          createdAt: new Date('2024-01-01T10:01:00Z'),
          updatedAt: new Date('2024-01-01T10:01:00Z'),
          sender: {
            id: testUser2.id,
            name: testUser2.name,
            image: testUser2.image,
          },
          seenBy: [],
        },
      ]

      mockPrisma.message.findMany.mockResolvedValue(mockMessages)

      const request = new NextRequest(
        `http://localhost:3000/api/conversations/${testConversation.id}/messages`
      )

      const response = await GET(request, {
        params: { conversationId: testConversation.id }
      })
      const data = await response.json()

      expect(response.status).toBe(200)
      expect(data.messages).toHaveLength(2)
      expect(data.messages[0].body).toBe('Hi there!')
      expect(data.messages[1].body).toBe('Hello!')
    })

    it('returns empty array when conversation has no messages', async () => {
      // Mock conversation exists
      mockPrisma.conversation.findFirst.mockResolvedValue(testConversation)
      // Mock no messages
      mockPrisma.message.findMany.mockResolvedValue([])

      const request = new NextRequest(
        `http://localhost:3000/api/conversations/${testConversation.id}/messages`
      )

      const response = await GET(request, {
        params: { conversationId: testConversation.id }
      })
      const data = await response.json()

      expect(response.status).toBe(200)
      expect(data.messages).toHaveLength(0)
    })

    it('returns 404 when conversation not found', async () => {
      // Mock conversation not found
      mockPrisma.conversation.findFirst.mockResolvedValue(null)

      const request = new NextRequest(
        'http://localhost:3000/api/conversations/nonexistent/messages'
      )

      const response = await GET(request, {
        params: { conversationId: 'nonexistent' }
      })
      const data = await response.json()

      expect(response.status).toBe(404)
      expect(data.error).toBe('Conversation not found')
    })

    it('returns 401 when user is not authenticated', async () => {
      mockGetServerSession.mockResolvedValue(null)

      const request = new NextRequest(
        `http://localhost:3000/api/conversations/${testConversation.id}/messages`
      )
      
      const response = await GET(request, { 
        params: { conversationId: testConversation.id } 
      })
      const data = await response.json()

      expect(response.status).toBe(401)
      expect(data.error).toBe('Unauthorized')
    })
  })

  describe('POST /api/conversations/[conversationId]/messages', () => {
    it('creates a new message', async () => {
      // Mock conversation exists
      mockPrisma.conversation.findFirst.mockResolvedValue(testConversation)

      // Mock created message
      const mockMessage = {
        id: 'new-msg-1',
        body: 'Test message',
        image: null,
        senderId: testUser1.id,
        conversationId: testConversation.id,
        createdAt: new Date(),
        updatedAt: new Date(),
        sender: {
          id: testUser1.id,
          name: testUser1.name,
          image: testUser1.image,
        },
        seenBy: [],
      }

      mockPrisma.message.create.mockResolvedValue(mockMessage)
      mockPrisma.conversation.update.mockResolvedValue(testConversation)

      const requestBody = {
        body: 'Test message',
      }

      const request = new NextRequest(
        `http://localhost:3000/api/conversations/${testConversation.id}/messages`,
        {
          method: 'POST',
          body: JSON.stringify(requestBody),
          headers: {
            'Content-Type': 'application/json',
          },
        }
      )

      const response = await POST(request, {
        params: { conversationId: testConversation.id }
      })
      const data = await response.json()

      expect(response.status).toBe(201)
      expect(data.message.body).toBe('Test message')
      expect(data.message.senderId).toBe(testUser1.id)
      expect(data.message.conversationId).toBe(testConversation.id)
    })

    it('creates a message with image', async () => {
      // Mock conversation exists
      mockPrisma.conversation.findFirst.mockResolvedValue(testConversation)

      // Mock created message with image
      const mockMessage = {
        id: 'new-msg-2',
        body: 'Check this out!',
        image: '/uploads/test-image.jpg',
        senderId: testUser1.id,
        conversationId: testConversation.id,
        createdAt: new Date(),
        updatedAt: new Date(),
        sender: {
          id: testUser1.id,
          name: testUser1.name,
          image: testUser1.image,
        },
        seenBy: [],
      }

      mockPrisma.message.create.mockResolvedValue(mockMessage)
      mockPrisma.conversation.update.mockResolvedValue(testConversation)

      const requestBody = {
        body: 'Check this out!',
        image: '/uploads/test-image.jpg',
      }

      const request = new NextRequest(
        `http://localhost:3000/api/conversations/${testConversation.id}/messages`,
        {
          method: 'POST',
          body: JSON.stringify(requestBody),
          headers: {
            'Content-Type': 'application/json',
          },
        }
      )

      const response = await POST(request, {
        params: { conversationId: testConversation.id }
      })
      const data = await response.json()

      expect(response.status).toBe(201)
      expect(data.message.body).toBe('Check this out!')
      expect(data.message.image).toBe('/uploads/test-image.jpg')
    })

    it('returns error when both body and image are missing', async () => {
      const requestBody = {}

      const request = new NextRequest(
        `http://localhost:3000/api/conversations/${testConversation.id}/messages`,
        {
          method: 'POST',
          body: JSON.stringify(requestBody),
          headers: {
            'Content-Type': 'application/json',
          },
        }
      )

      const response = await POST(request, { 
        params: { conversationId: testConversation.id } 
      })
      const data = await response.json()

      expect(response.status).toBe(400)
      expect(data.error).toBe('Message body or image is required')
    })

    it('returns 404 when conversation not found', async () => {
      const requestBody = {
        body: 'Test message',
      }

      const request = new NextRequest(
        'http://localhost:3000/api/conversations/nonexistent/messages',
        {
          method: 'POST',
          body: JSON.stringify(requestBody),
          headers: {
            'Content-Type': 'application/json',
          },
        }
      )

      const response = await POST(request, { 
        params: { conversationId: 'nonexistent' } 
      })
      const data = await response.json()

      expect(response.status).toBe(404)
      expect(data.error).toBe('Conversation not found')
    })

    it('returns 401 when user is not authenticated', async () => {
      mockGetServerSession.mockResolvedValue(null)

      const requestBody = {
        body: 'Test message',
      }

      const request = new NextRequest(
        `http://localhost:3000/api/conversations/${testConversation.id}/messages`,
        {
          method: 'POST',
          body: JSON.stringify(requestBody),
          headers: {
            'Content-Type': 'application/json',
          },
        }
      )

      const response = await POST(request, { 
        params: { conversationId: testConversation.id } 
      })
      const data = await response.json()

      expect(response.status).toBe(401)
      expect(data.error).toBe('Unauthorized')
    })
  })
})
