import { NextRequest } from 'next/server'
import { GET, POST } from '../route'
import { prisma } from '@/lib/prisma'

// Mock NextAuth
jest.mock('next-auth', () => ({
  getServerSession: jest.fn(),
}))

import { getServerSession } from 'next-auth'
const mockGetServerSession = getServerSession as jest.MockedFunction<typeof getServerSession>
const mockPrisma = prisma as jest.Mocked<typeof prisma>

describe('/api/conversations', () => {
  const testUser1 = {
    id: 'test-user-1',
    name: 'Test User 1',
    email: '<EMAIL>',
    hashedPassword: 'hashed-password',
    image: null,
    createdAt: new Date(),
    updatedAt: new Date(),
  }

  const testUser2 = {
    id: 'test-user-2',
    name: 'Test User 2',
    email: '<EMAIL>',
    hashedPassword: 'hashed-password',
    image: null,
    createdAt: new Date(),
    updatedAt: new Date(),
  }

  beforeEach(() => {
    jest.clearAllMocks()

    mockGetServerSession.mockResolvedValue({
      user: { id: testUser1.id },
    } as any)
  })

  describe('GET /api/conversations', () => {
    it('returns user conversations', async () => {
      // Mock conversation data
      const mockConversation = {
        id: 'conv-1',
        isGroup: false,
        name: null,
        createdAt: new Date(),
        updatedAt: new Date(),
        participants: [
          {
            userId: testUser1.id,
            conversationId: 'conv-1',
            user: testUser1,
          },
          {
            userId: testUser2.id,
            conversationId: 'conv-1',
            user: testUser2,
          },
        ],
        messages: [],
        _count: { messages: 0 },
      }

      mockPrisma.conversation.findMany.mockResolvedValue([mockConversation])

      const request = new NextRequest('http://localhost:3000/api/conversations')
      const response = await GET(request)
      const data = await response.json()

      expect(response.status).toBe(200)
      expect(data.conversations).toHaveLength(1)
      expect(data.conversations[0].participants).toHaveLength(2)
    })

    it('returns empty array when user has no conversations', async () => {
      mockPrisma.conversation.findMany.mockResolvedValue([])

      const request = new NextRequest('http://localhost:3000/api/conversations')
      const response = await GET(request)
      const data = await response.json()

      expect(response.status).toBe(200)
      expect(data.conversations).toHaveLength(0)
    })

    it('returns 401 when user is not authenticated', async () => {
      mockGetServerSession.mockResolvedValue(null)

      const request = new NextRequest('http://localhost:3000/api/conversations')
      const response = await GET(request)
      const data = await response.json()

      expect(response.status).toBe(401)
      expect(data.error).toBe('Unauthorized')
    })
  })

  describe('POST /api/conversations', () => {
    it('creates a new one-on-one conversation', async () => {
      // Mock no existing conversation
      mockPrisma.conversation.findFirst.mockResolvedValue(null)

      // Mock created conversation
      const mockConversation = {
        id: 'new-conv-1',
        isGroup: false,
        name: null,
        createdAt: new Date(),
        updatedAt: new Date(),
        participants: [
          {
            userId: testUser1.id,
            conversationId: 'new-conv-1',
            user: testUser1,
          },
          {
            userId: testUser2.id,
            conversationId: 'new-conv-1',
            user: testUser2,
          },
        ],
      }

      mockPrisma.conversation.create.mockResolvedValue(mockConversation)

      const requestBody = {
        participantIds: [testUser2.id],
        isGroup: false,
      }

      const request = new NextRequest('http://localhost:3000/api/conversations', {
        method: 'POST',
        body: JSON.stringify(requestBody),
        headers: {
          'Content-Type': 'application/json',
        },
      })

      const response = await POST(request)
      const data = await response.json()

      expect(response.status).toBe(201)
      expect(data.conversation.isGroup).toBe(false)
      expect(data.conversation.participants).toHaveLength(2)
    })

    it('creates a new group conversation', async () => {
      const testUser3 = {
        id: 'test-user-3',
        name: 'Test User 3',
        email: '<EMAIL>',
        hashedPassword: 'hashed-password',
        image: null,
        createdAt: new Date(),
        updatedAt: new Date(),
      }

      // Mock created group conversation
      const mockGroupConversation = {
        id: 'group-conv-1',
        isGroup: true,
        name: 'Test Group',
        createdAt: new Date(),
        updatedAt: new Date(),
        participants: [
          {
            userId: testUser1.id,
            conversationId: 'group-conv-1',
            user: testUser1,
          },
          {
            userId: testUser2.id,
            conversationId: 'group-conv-1',
            user: testUser2,
          },
          {
            userId: testUser3.id,
            conversationId: 'group-conv-1',
            user: testUser3,
          },
        ],
      }

      mockPrisma.conversation.create.mockResolvedValue(mockGroupConversation)

      const requestBody = {
        participantIds: [testUser2.id, testUser3.id],
        isGroup: true,
        name: 'Test Group',
      }

      const request = new NextRequest('http://localhost:3000/api/conversations', {
        method: 'POST',
        body: JSON.stringify(requestBody),
        headers: {
          'Content-Type': 'application/json',
        },
      })

      const response = await POST(request)
      const data = await response.json()

      expect(response.status).toBe(201)
      expect(data.conversation.isGroup).toBe(true)
      expect(data.conversation.name).toBe('Test Group')
      expect(data.conversation.participants).toHaveLength(3) // Including current user
    })

    it('returns existing conversation for one-on-one chat', async () => {
      // Mock existing conversation
      const existingConversation = {
        id: 'existing-conv-1',
        isGroup: false,
        name: null,
        createdAt: new Date(),
        updatedAt: new Date(),
        participants: [
          {
            userId: testUser1.id,
            conversationId: 'existing-conv-1',
            user: testUser1,
          },
          {
            userId: testUser2.id,
            conversationId: 'existing-conv-1',
            user: testUser2,
          },
        ],
      }

      mockPrisma.conversation.findFirst.mockResolvedValue(existingConversation)

      const requestBody = {
        participantIds: [testUser2.id],
        isGroup: false,
      }

      const request = new NextRequest('http://localhost:3000/api/conversations', {
        method: 'POST',
        body: JSON.stringify(requestBody),
        headers: {
          'Content-Type': 'application/json',
        },
      })

      const response = await POST(request)
      const data = await response.json()

      expect(response.status).toBe(200)
      expect(data.conversation.id).toBe(existingConversation.id)
    })

    it('returns error for missing participant IDs', async () => {
      const requestBody = {
        isGroup: false,
      }

      const request = new NextRequest('http://localhost:3000/api/conversations', {
        method: 'POST',
        body: JSON.stringify(requestBody),
        headers: {
          'Content-Type': 'application/json',
        },
      })

      const response = await POST(request)
      const data = await response.json()

      expect(response.status).toBe(400)
      expect(data.error).toBe('Participant IDs are required')
    })

    it('returns 401 when user is not authenticated', async () => {
      mockGetServerSession.mockResolvedValue(null)

      const requestBody = {
        participantIds: [testUser2.id],
        isGroup: false,
      }

      const request = new NextRequest('http://localhost:3000/api/conversations', {
        method: 'POST',
        body: JSON.stringify(requestBody),
        headers: {
          'Content-Type': 'application/json',
        },
      })

      const response = await POST(request)
      const data = await response.json()

      expect(response.status).toBe(401)
      expect(data.error).toBe('Unauthorized')
    })
  })
})
