require('@testing-library/jest-dom')

// Polyfill for Next.js environment
const { TextEncoder, TextDecoder } = require('util')
global.TextEncoder = TextEncoder
global.TextDecoder = TextDecoder

// Simple polyfill for Request/Response/Headers that works with Next.js
if (typeof global.Request === 'undefined') {
  global.Request = class Request {
    constructor(input, init = {}) {
      // Don't set url property directly to avoid conflicts
      Object.defineProperty(this, 'url', {
        value: typeof input === 'string' ? input : input.url,
        writable: false,
        enumerable: true,
        configurable: true
      })
      this.method = init.method || 'GET'
      this.headers = new Map(Object.entries(init.headers || {}))
      this.body = init.body
    }

    async json() {
      return JSON.parse(this.body || '{}')
    }

    async text() {
      return this.body || ''
    }
  }
}

if (typeof global.Response === 'undefined') {
  global.Response = class Response {
    constructor(body, init = {}) {
      this.body = body
      this.status = init.status || 200
      this.statusText = init.statusText || 'OK'
      this.headers = new Map(Object.entries(init.headers || {}))
      this.ok = this.status >= 200 && this.status < 300
    }

    static json(data, init = {}) {
      return new Response(JSON.stringify(data), {
        ...init,
        headers: { 'Content-Type': 'application/json', ...init.headers }
      })
    }

    async json() {
      return JSON.parse(this.body)
    }

    async text() {
      return this.body
    }
  }
}

if (typeof global.Headers === 'undefined') {
  global.Headers = class Headers {
    constructor(init = {}) {
      this.map = new Map(Object.entries(init || {}))
    }

    get(name) {
      return this.map.get(name.toLowerCase())
    }

    set(name, value) {
      this.map.set(name.toLowerCase(), value)
    }

    has(name) {
      return this.map.has(name.toLowerCase())
    }

    entries() {
      return this.map.entries()
    }
  }
}

// Mock fetch with proper default responses
global.fetch = jest.fn().mockImplementation((url, options) => {
  // Default successful response
  return Promise.resolve({
    ok: true,
    status: 200,
    statusText: 'OK',
    headers: new Map([['content-type', 'application/json']]),
    json: () => Promise.resolve({ success: true }),
    text: () => Promise.resolve('{"success": true}'),
  })
})

// Mock Next.js router
jest.mock('next/navigation', () => ({
  useRouter() {
    return {
      push: jest.fn(),
      replace: jest.fn(),
      prefetch: jest.fn(),
      back: jest.fn(),
      forward: jest.fn(),
      refresh: jest.fn(),
    }
  },
  useSearchParams() {
    return new URLSearchParams()
  },
  usePathname() {
    return '/'
  },
}))

// Mock NextAuth
jest.mock('next-auth/react', () => ({
  useSession() {
    return {
      data: {
        user: {
          id: 'test-user-id',
          name: 'Test User',
          email: '<EMAIL>',
          image: null,
        },
      },
      status: 'authenticated',
    }
  },
  signIn: jest.fn(),
  signOut: jest.fn(),
  SessionProvider: function({ children }) { return children; },
}))

// Mock NextAuth server-side
jest.mock('next-auth', () => ({
  getServerSession: jest.fn().mockResolvedValue({
    user: {
      id: 'test-user-id',
      name: 'Test User',
      email: '<EMAIL>',
      image: null,
    },
  }),
}))

// Mock Socket.IO
jest.mock('@/components/providers/socket-provider', () => ({
  useSocket() {
    return {
      socket: {
        emit: jest.fn(),
        on: jest.fn(),
        off: jest.fn(),
        disconnect: jest.fn(),
      },
      isConnected: true,
    }
  },
  __esModule: true,
  default: function({ children }) { return children; },
}))

// Mock file upload (fetch already mocked above)

// Mock Prisma clients
jest.mock('@/lib/prisma', () => ({
  prisma: {
    user: {
      create: jest.fn(),
      findUnique: jest.fn(),
      findMany: jest.fn(),
      findFirst: jest.fn(),
      update: jest.fn(),
      delete: jest.fn(),
    },
    conversation: {
      create: jest.fn(),
      findMany: jest.fn(),
      findUnique: jest.fn(),
      findFirst: jest.fn(),
      update: jest.fn(),
      delete: jest.fn(),
    },
    message: {
      create: jest.fn(),
      findMany: jest.fn(),
      findUnique: jest.fn(),
      findFirst: jest.fn(),
      update: jest.fn(),
      delete: jest.fn(),
    },
    $disconnect: jest.fn(),
  },
}))

jest.mock('@/lib/test-db', () => ({
  prismaTest: {
    user: {
      create: jest.fn(),
      findUnique: jest.fn(),
      findMany: jest.fn(),
      update: jest.fn(),
      delete: jest.fn(),
    },
    conversation: {
      create: jest.fn(),
      findMany: jest.fn(),
      findUnique: jest.fn(),
      update: jest.fn(),
      delete: jest.fn(),
    },
    message: {
      create: jest.fn(),
      findMany: jest.fn(),
      findUnique: jest.fn(),
      update: jest.fn(),
      delete: jest.fn(),
    },
    $queryRaw: jest.fn().mockResolvedValue([]),
    $executeRawUnsafe: jest.fn().mockResolvedValue(undefined),
    $disconnect: jest.fn(),
  },
  cleanupDatabase: jest.fn().mockResolvedValue(undefined),
  createTestUser: jest.fn().mockResolvedValue({
    id: 'test-user-id',
    name: 'Test User',
    email: '<EMAIL>',
    hashedPassword: 'hashed-password',
    createdAt: new Date(),
    updatedAt: new Date(),
  }),
  createTestConversation: jest.fn().mockResolvedValue({
    id: 'test-conversation-id',
    isGroup: false,
    name: null,
    createdAt: new Date(),
    updatedAt: new Date(),
    participants: [],
  }),
  createTestMessage: jest.fn().mockResolvedValue({
    id: 'test-message-id',
    body: 'Test message',
    image: null,
    senderId: 'test-user-id',
    conversationId: 'test-conversation-id',
    createdAt: new Date(),
    updatedAt: new Date(),
    sender: { id: 'test-user-id', name: 'Test User', email: '<EMAIL>' },
    seenBy: [],
  }),
}))

// Mock window.matchMedia (only in jsdom environment)
if (typeof window !== 'undefined') {
  Object.defineProperty(window, 'matchMedia', {
    writable: true,
    value: jest.fn().mockImplementation(query => ({
      matches: false,
      media: query,
      onchange: null,
      addListener: jest.fn(), // deprecated
      removeListener: jest.fn(), // deprecated
      addEventListener: jest.fn(),
      removeEventListener: jest.fn(),
      dispatchEvent: jest.fn(),
    })),
  })
}

// Mock IntersectionObserver
global.IntersectionObserver = jest.fn().mockImplementation(() => ({
  observe: jest.fn(),
  unobserve: jest.fn(),
  disconnect: jest.fn(),
}))

// Mock ResizeObserver
global.ResizeObserver = jest.fn().mockImplementation(() => ({
  observe: jest.fn(),
  unobserve: jest.fn(),
  disconnect: jest.fn(),
}))
