import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { SessionProvider } from 'next-auth/react'
import ChatLayout from '@/components/chat/chat-layout'
import { cleanupDatabase, createTestUser, createTestConversation } from '@/lib/test-db'

// Mock Socket.IO
const mockSocket = {
  emit: jest.fn(),
  on: jest.fn(),
  off: jest.fn(),
  disconnect: jest.fn(),
}

jest.mock('@/components/providers/socket-provider', () => ({
  useSocket: () => ({
    socket: mockSocket,
    isConnected: true,
  }),
  __esModule: true,
  default: ({ children }: { children: React.ReactNode }) => <>{children}</>,
}))

// Mock fetch - will override the global mock for this test
const mockFetch = jest.fn()

// Mock session
const mockSession = {
  user: {
    id: 'test-user-1',
    name: 'Test User',
    email: '<EMAIL>',
    image: null,
  },
  expires: '2024-12-31',
}

const TestWrapper = ({ children }: { children: React.ReactNode }) => (
  <SessionProvider session={mockSession}>
    {children}
  </SessionProvider>
)

describe('Chat Application Integration', () => {
  beforeEach(() => {
    jest.clearAllMocks()

    // Override global fetch mock for this test suite
    global.fetch = mockFetch

    // Mock successful API responses
    mockFetch.mockImplementation((url: string) => {
      if (url.includes('/api/conversations')) {
        return Promise.resolve({
          ok: true,
          json: () => Promise.resolve({
            conversations: [
              {
                id: 'conv-1',
                name: null,
                isGroup: false,
                participants: [
                  {
                    user: {
                      id: 'test-user-1',
                      name: 'Test User',
                      email: '<EMAIL>',
                    },
                  },
                  {
                    user: {
                      id: 'test-user-2',
                      name: 'Other User',
                      email: '<EMAIL>',
                    },
                  },
                ],
                messages: [],
              },
            ],
          }),
        })
      }
      
      if (url.includes('/messages')) {
        return Promise.resolve({
          ok: true,
          json: () => Promise.resolve({
            messages: [
              {
                id: 'msg-1',
                body: 'Hello there!',
                createdAt: new Date().toISOString(),
                senderId: 'test-user-2',
                conversationId: 'conv-1',
                sender: {
                  id: 'test-user-2',
                  name: 'Other User',
                },
                seenBy: [],
              },
            ],
          }),
        })
      }
      
      if (url.includes('/api/users/search')) {
        return Promise.resolve({
          ok: true,
          json: () => Promise.resolve({
            users: [
              {
                id: 'test-user-3',
                name: 'New User',
                email: '<EMAIL>',
              },
            ],
          }),
        })
      }
      
      return Promise.resolve({
        ok: true,
        json: () => Promise.resolve({}),
      })
    })
  })

  it('renders chat layout and loads conversations', async () => {
    render(
      <TestWrapper>
        <ChatLayout />
      </TestWrapper>
    )

    // Should show loading initially, then conversations
    await waitFor(() => {
      expect(screen.getByText('Test User')).toBeInTheDocument()
    })

    // Should show online status
    expect(screen.getByText('Online')).toBeInTheDocument()
  })

  it('allows user to start a new conversation', async () => {
    const user = userEvent.setup()
    
    render(
      <TestWrapper>
        <ChatLayout />
      </TestWrapper>
    )

    // Wait for component to load
    await waitFor(() => {
      expect(screen.getByText('Test User')).toBeInTheDocument()
    })

    // Click new chat button
    const newChatButton = screen.getByText('New Chat')
    await user.click(newChatButton)

    // Should open user search modal
    await waitFor(() => {
      expect(screen.getByText('Start New Conversation')).toBeInTheDocument()
    })

    // Search for users
    const searchInput = screen.getByPlaceholderText('Search users by name or email...')
    await user.type(searchInput, 'new')

    // Should show search results
    await waitFor(() => {
      expect(screen.getByText('New User')).toBeInTheDocument()
    })
  })

  it('allows user to create a group chat', async () => {
    const user = userEvent.setup()
    
    render(
      <TestWrapper>
        <ChatLayout />
      </TestWrapper>
    )

    // Wait for component to load
    await waitFor(() => {
      expect(screen.getByText('Test User')).toBeInTheDocument()
    })

    // Click new group button
    const newGroupButton = screen.getByText('New Group')
    await user.click(newGroupButton)

    // Should open group chat modal
    await waitFor(() => {
      expect(screen.getByText('Create Group Chat')).toBeInTheDocument()
    })

    // Enter group name
    const groupNameInput = screen.getByPlaceholderText('Enter group name...')
    await user.type(groupNameInput, 'Test Group')

    // Search for users to add
    const searchInput = screen.getByPlaceholderText('Search users by name or email...')
    await user.type(searchInput, 'new')

    // Should show search results
    await waitFor(() => {
      expect(screen.getByText('New User')).toBeInTheDocument()
    })
  })

  it('displays messages in a conversation', async () => {
    render(
      <TestWrapper>
        <ChatLayout />
      </TestWrapper>
    )

    // Wait for conversations to load
    await waitFor(() => {
      expect(screen.getByText('Test User')).toBeInTheDocument()
    })

    // Mock joining a conversation (this would normally happen when clicking on a conversation)
    // For this test, we'll simulate the state where a conversation is selected
    await waitFor(() => {
      // The chat area should show when a conversation is selected
      // This is a simplified test - in reality, we'd need to click on a conversation
      expect(mockFetch).toHaveBeenCalledWith('/api/conversations')
    })
  })

  it('handles socket events correctly', async () => {
    render(
      <TestWrapper>
        <ChatLayout />
      </TestWrapper>
    )

    // Wait for component to load
    await waitFor(() => {
      expect(screen.getByText('Test User')).toBeInTheDocument()
    })

    // Verify socket event listeners were registered for chat functionality
    expect(mockSocket.on).toHaveBeenCalledWith('new-message', expect.any(Function))
    expect(mockSocket.on).toHaveBeenCalledWith('user-typing', expect.any(Function))
    expect(mockSocket.on).toHaveBeenCalledWith('user-stop-typing', expect.any(Function))
    expect(mockSocket.on).toHaveBeenCalledWith('user-status-change', expect.any(Function))
  })

  it('handles responsive design', async () => {
    // Mock mobile viewport
    Object.defineProperty(window, 'innerWidth', {
      writable: true,
      configurable: true,
      value: 500,
    })

    render(
      <TestWrapper>
        <ChatLayout />
      </TestWrapper>
    )

    // Trigger resize event
    fireEvent(window, new Event('resize'))

    // Wait for component to load
    await waitFor(() => {
      expect(screen.getByText('Test User')).toBeInTheDocument()
    })

    // On mobile, sidebar should be hidden initially
    // This is a simplified test - actual implementation would need more detailed testing
  })
})

describe('Error Handling', () => {
  beforeEach(() => {
    jest.clearAllMocks()
  })

  it('handles API errors gracefully', async () => {
    // Mock API error
    mockFetch.mockRejectedValue(new Error('Network error'))

    render(
      <TestWrapper>
        <ChatLayout />
      </TestWrapper>
    )

    // Should still render without crashing
    await waitFor(() => {
      expect(screen.getByText('Test User')).toBeInTheDocument()
    })
  })

  it('handles socket disconnection', async () => {
    render(
      <TestWrapper>
        <ChatLayout />
      </TestWrapper>
    )

    // Simulate socket disconnect
    const disconnectHandler = mockSocket.on.mock.calls.find(
      call => call[0] === 'disconnect'
    )?.[1]

    if (disconnectHandler) {
      disconnectHandler()
    }

    // Should handle disconnection gracefully
    await waitFor(() => {
      expect(screen.getByText('Test User')).toBeInTheDocument()
    })
  })
})
